package com.dc.proxy.controller;

import com.dc.proxy.service.ZombieObjecServiceFactory;
import com.dc.springboot.core.model.database.ConnectionMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "僵尸对象控制器")
@Slf4j
@RequestMapping("/zombie-object")
public class ZombieObjectController {

    @Resource
    private ZombieObjecServiceFactory zombieObjecServiceFactory;

    /**
     * @param connectionMessage connection message
     * @see <a href="http://192.168.1.212/zentao/story-view-1054.html">需求文档</a>
     */
    @ApiOperation("更新实例对象最后访问时间")
    @PostMapping
    public void updateZombieObject(@Valid @RequestBody ConnectionMessage connectionMessage) {
        log.debug("REST request to zombie-object, instance name is {}", connectionMessage.getConnectionConfig().getInstanceName());
        zombieObjecServiceFactory.executeZombieObjecService(connectionMessage);
    }
}
