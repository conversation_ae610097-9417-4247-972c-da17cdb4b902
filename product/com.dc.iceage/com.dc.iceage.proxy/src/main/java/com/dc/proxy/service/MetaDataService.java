package com.dc.proxy.service;

import com.dc.proxy.model.data.message.HiveMetaDataMessage;
import com.dc.proxy.model.result.CatalogSchemaResult;
import com.dc.proxy.model.result.HiveResult;
import com.dc.springboot.core.model.database.ConnectionMessage;
import com.dc.springboot.core.model.database.TableFieldsMessage;
import com.dc.summer.model.sql.SqlFieldData;

import java.util.List;

public interface MetaDataService {

    /**
     * 获取 Hive 元信息，查表、视图、列、函数
     */
    HiveResult hiveMetadata(HiveMetaDataMessage message);

    /**
     * 获取 catalog、schema 用户名和计数
     */
    List<CatalogSchemaResult> getSchemas(ConnectionMessage message);

    /**
     * 读取当前数据库版本
     */
    String getVersion(ConnectionMessage message);

    /**
     * 获取表的列名、列类型、列长度、是否主键
     */
    List<SqlFieldData> getTableFields(TableFieldsMessage message);

}
