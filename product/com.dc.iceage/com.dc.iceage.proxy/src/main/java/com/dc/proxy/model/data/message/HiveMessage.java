package com.dc.proxy.model.data.message;

import com.dc.springboot.core.model.database.ConnectionMessage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("Hive消息")
public class HiveMessage extends ConnectionMessage {

    @NotNull
    @ApiModelProperty(value = "类型", required = true, example = "1")
    private Integer type;

    @NotBlank
    @ApiModelProperty(value = "统配模式", required = true, example = "%")
    private String pattern;

    @NotBlank
    @ApiModelProperty(value = "模式", required = true, example = "%")
    private String schemaName;

}
