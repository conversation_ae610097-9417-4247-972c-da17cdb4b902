package com.dc.proxy.model.receiver;

import com.dc.springboot.core.model.data.RowsData;
import com.dc.summer.model.exec.*;
import com.dc.summer.exec.model.data.AbstractDataReceiver;
import com.dc.summer.model.impl.jdbc.data.JDBCContentAbstract;
import com.dc.summer.model.impl.jdbc.data.JDBCContentBytes;
import com.dc.summer.model.impl.jdbc.data.JDBCContentCLOB;
import com.dc.summer.model.impl.jdbc.data.JDBCContentChars;
import com.dc.summer.model.impl.struct.SimpleContainer;
import com.dc.summer.utils.ContentUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.Reader;

@Slf4j
public class ResultDataReceiver extends AbstractDataReceiver {

    protected final RowsData rowsData = new RowsData();

    public ResultDataReceiver(DBCExecutionContext executionContext) {
        super(new SimpleContainer(executionContext.getDataSource()));
    }

    @Override
    public void fetchRow(DBCSession session, DBCResultSet resultSet) throws DBCException {
        rowsData.add(getRowMap(resultSet, o -> {
            if (o instanceof JDBCContentBytes) {
                return null;
            } else if (o instanceof JDBCContentCLOB || o instanceof JDBCContentChars) {
                if (((JDBCContentAbstract) o).isNull()) {
                    return null;
                }
                if (o instanceof JDBCContentCLOB) {
                    String result = "";
                    byte[] binaryValue = null;
                    JDBCContentCLOB value = (JDBCContentCLOB) o;
                    try(Reader reader = value.getContents(AbstractDataReceiver.monitor).getContentReader()) {
                        binaryValue = ContentUtils.clobToBytes(reader, value.getContentLength());
                        result =  new String(binaryValue);
                    } catch (Exception e) {
                        result = e.getMessage();
                    } finally {
                        value.resetContents();
                        value.release();
                    }
                    return result;
                }
                return o.toString();
            }
            if (o instanceof String) {
                o = o.toString().replaceAll("\u0000", " ");
            }
            return o;
        }));
    }

    public RowsData getRowsData() {
        RowsData copy = new RowsData(rowsData.size());
        boolean b = copy.addAll(rowsData);
        return copy;
    }

    public void clear() {
        rowsData.clear();
    }

    @Override
    public void fetchEnd(DBCSession session, DBCResultSet resultSet) throws DBCException {
    }

    @Override
    public void close() {
        bindings = null;
        rowsData.clear();
    }
}
