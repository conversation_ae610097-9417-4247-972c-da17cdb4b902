package com.dc.proxy.model.type;

public enum IntroduceType {
    NSET("NSET","RESULT SET"),
    PRJT("PRJT","PROJECT OPERATION"),
    SORT("SORT","SORT"),
    SLCT("SLCT","SELECT"),
    NEST_LOOP_INNER_JOIN("NEST LOOP INNER JOIN","self"),
    CSEK("CSEK","CLUSYER INDEX SEEK"),
    CSCN("CSCN","CLUSTER SCAN");

    public String name;

    public String value;

    IntroduceType(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByName(String name) {
        for (IntroduceType type : IntroduceType.values()) {
            if (type.getName().equals(name)) {
                return type.getValue();
            }
        }
        return null;
    }
}
