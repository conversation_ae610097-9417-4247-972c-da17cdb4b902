package com.dc.proxy.service.impl;

import com.dc.annotation.SQL;
import com.dc.iceage.model.jdbc.ExecutionContainer;
import com.dc.proxy.model.data.ExplainDataResult;
import com.dc.proxy.model.receiver.MultipleResultDataReceiver;
import com.dc.proxy.model.receiver.ResultDataReceiver;
import com.dc.proxy.model.type.ExplainShowType;
import com.dc.proxy.service.ExecuteService;
import com.dc.springboot.core.model.data.ResultData;
import com.dc.springboot.core.model.data.RowsData;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.execution.SqlListExecuteMessage;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;
import com.dc.springboot.core.service.ParserService;
import com.dc.summer.DBException;
import com.dc.summer.ext.dm.model.LVal;
import com.dc.summer.ext.dm.model.utils.StringUtil;
import com.dc.summer.ext.dm.plan.PlanParser;
import com.dc.summer.ext.dm.plan.PlanTreeNode;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.type.DatabaseType;
import com.dc.utils.bean.ReflectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ExecuteServiceImpl implements ExecuteService {

    private final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

    private final int defaultFetchSize = 100;

    private final int slowCost = 15000;

    @Resource
    private List<ParserService> parserServiceList;

    @Resource
    private ParserService parserServiceImpl;

    @Override
    public List<RowsData> execute(SqlListExecuteMessage message) {

        List<RowsData> result = new ArrayList<>(message.getSqlList().size());

        try (ExecutionContainer executionContainer = new ExecutionContainer(message.getConnectionConfig().getConnectionConfiguration(), message.isAutoCloseDataSource())) {
            DBCExecutionContext executionContext = executionContainer.getAndSetExecutionContextDefaults();
            ResultDataReceiver dataReceiver = new ResultDataReceiver(executionContext);
            for (@SQL String sql : message.getSqlList()) {
                try (dataReceiver) {
                    long currentTimeMillis = System.currentTimeMillis();
                    DBExecUtils.tryExecuteRecover(monitor, executionContext.getDataSource(), param -> {
                        try {
                            DBExecUtils.executeScript(monitor, executionContext, "Execute SQL", sql, dataReceiver, defaultFetchSize);
                        } catch (DBException e) {
                            throw new InvocationTargetException(e);
                        }
                        result.add(dataReceiver.getRowsData());
                    });
                    long cost = System.currentTimeMillis() - currentTimeMillis;
                    if (cost > slowCost) {
                        log.warn("[Meta慢查询] 执行SQL耗时: {}ms, connection: {}, sql: \n{}", cost, message.getConnectionConfig().getInstanceName(), sql);
                    }
                }
            }
        } catch (Exception e) {
            extractException(e);
        }

        return result;
    }

    @Override
    public List<ResultData> multipleExecute(SqlListExecuteMessage message) {
        List<ResultData> result = new ArrayList<>(message.getSqlList().size());

        try (ExecutionContainer executionContainer = new ExecutionContainer(message.getConnectionConfig().getConnectionConfiguration(), message.isAutoCloseDataSource())) {
            DBCExecutionContext executionContext = executionContainer.getAndSetExecutionContextDefaults();
            MultipleResultDataReceiver dataReceiver = new MultipleResultDataReceiver(executionContext);
            for (@SQL String sql : message.getSqlList()) {
                try (dataReceiver) {
                    long currentTimeMillis = System.currentTimeMillis();
                    DBExecUtils.tryExecuteRecover(monitor, executionContext.getDataSource(), param -> {
                        try {
                            DBExecUtils.executeWithMultipleResultSet(monitor, executionContext, "Execute SQL", sql, dataReceiver, defaultFetchSize);
                        } catch (DBException e) {
                            throw new InvocationTargetException(e);
                        }
                        result.add(dataReceiver.getResultData());
                    });
                    long cost = System.currentTimeMillis() - currentTimeMillis;
                    if (cost > slowCost) {
                        log.warn("[Meta慢查询] 执行SQL耗时: {}ms, connection: {}, sql: \n{}", cost, message.getConnectionConfig().getInstanceName(), sql);
                    }
                }
            }
        } catch (Exception e) {
            extractException(e);
        }

        return result;
    }

    @Override
    public List<ExplainDataResult> explain(SqlListExecuteMessage message) {
        try (ExecutionContainer executionContainer = new ExecutionContainer(message.getConnectionConfig().getConnectionConfiguration(), message.isAutoCloseDataSource())) {
            DBCExecutionContext executionContext = executionContainer.getAndSetExecutionContextDefaults();
            if (executionContext instanceof JDBCExecutionContext) {

                Map<String, Object> nodeMap = new HashMap<>();
                try (ResultDataReceiver dataReceiver = new ResultDataReceiver(executionContext);) {
                    DBExecUtils.tryExecuteRecover(monitor, executionContext.getDataSource(), param -> {
                        try {
                            DBExecUtils.executeScript(monitor, executionContext, "Get DESC CONTENT", "select NAME,DESC_CONTENT from SYS.V$SQL_NODE_NAME", dataReceiver, defaultFetchSize);
                        } catch (DBException e) {
                            throw new InvocationTargetException(e);
                        }
                        RowsData rowsData = dataReceiver.getRowsData();
                        for (Map<String, Object> row : rowsData) {
                            nodeMap.put(row.get("NAME").toString(), row.get("DESC_CONTENT"));
                        }
                    });
                }

                Connection connection = ((JDBCExecutionContext) executionContext).getConnection(monitor);
                connection = connection.unwrap(Connection.class);

                List<ExplainDataResult> result = new ArrayList<>();
                for (String sql : message.getSqlList()) {
                    ExplainDataResult explainDataResult = new ExplainDataResult();
                    String warning;
                    if (sql.length() > 7) {
                        String sqlBegin = sql.substring(0, 7);
                        if (sqlBegin.toLowerCase().startsWith("explain")) {
                            List<LVal> lvalList = StringUtil.lexN(sql, 2, new LVal.Type[]{LVal.Type.LITERAL});
                            String first = lvalList.size() > 0 ? ((LVal)lvalList.get(0)).value : null;
                            warning = lvalList.size() > 1 ? ((LVal)lvalList.get(1)).value : null;
                            if (StringUtil.equalsIgnoreCase(first, "explain") && !StringUtil.equalsIgnoreCase(warning, "for")) {
                                sql = sql.substring(7).trim();
                            }
                        }
                    }
                    String eP = (String) ReflectUtils.executeMethodForObj(connection, "getExplainInfo", new Class[]{String.class}, new String[]{sql});
                    if (ExplainShowType.STRING.getValue().equals(message.getShowType())) {
                        explainDataResult.setContent(eP);
                    } else {
                        List<PlanTreeNode> planTreeNodeList = new ArrayList<>();
                        planTreeNodeList.add((new PlanParser()).parse(eP, nodeMap));
                        explainDataResult.setExplainResults(planTreeNodeList);
                    }
                    result.add(explainDataResult);
                }
                return result;

            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage(), e);
        }
        return List.of();
    }

    @Override
    public WebSQLScriptInfo parseScript(ParseScriptMessage message) {
        for (ParserService parserService : parserServiceList) {
            if (parserService.supportsThisType(DatabaseType.of(message.getDatabaseType()))) {
                return parserService.parseSqlScript(message);
            }
        }
        return parserServiceImpl.parseSqlScript(message);
    }

    private static void extractException(Exception e) {
        String errorMessage;
        Throwable throwable = e;
        while (throwable.getCause() != null && throwable.getCause().getMessage() != null) {
            throwable = throwable.getCause();
        }
        errorMessage = throwable.getMessage();
        throw new ServiceException(errorMessage, e);
    }

}
