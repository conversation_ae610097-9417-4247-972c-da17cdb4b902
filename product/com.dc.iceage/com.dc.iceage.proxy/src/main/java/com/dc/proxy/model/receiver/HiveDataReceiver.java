package com.dc.proxy.model.receiver;

import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.summer.model.data.DBDAttributeBinding;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBCResultSet;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.data.DBDValueError;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class HiveDataReceiver extends ResultDataReceiver {

    private List<String> instances;

    private Map<String, String> propertyMap;


    public HiveDataReceiver(DBCExecutionContext executionContext, Map<String, String> propertyMap, List<String> instances) {
        super(executionContext);
        this.instances = instances;
        this.propertyMap = propertyMap;
    }

    public HiveDataReceiver(DBCExecutionContext executionContext, Map<String, String> propertyMap) {
        super(executionContext);
        this.propertyMap = propertyMap;
    }

    public HiveDataReceiver(DBCExecutionContext executionContext) {
        super(executionContext);
    }

    @Override
    public void fetchRow(DBCSession session, DBCResultSet resultSet) throws DBCException {
        Map<String, Object> rowMap = getRowMap(resultSet);
        boolean isGradingAdmin = instances != null && instances.size() > 0;
        if (isGradingAdmin) {
            String gradingTable = String.format("%s.%s", rowMap.get("owner"), rowMap.get("object_name"));
            if (instances.contains(gradingTable) || instances.contains((String)rowMap.get("owner"))) {
                rowsData.add(rowMap);
            } else {
                throw new ServiceException("不是分级管理员");
            }
        } else {
            rowsData.add(rowMap);
        }
    }

    @Override
    protected Map<String, Object> getRowMap(DBCResultSet resultSet) {
        Map<String, Object> object = new LinkedHashMap<>();

        for (int i = 0; i < bindings.length; i++) {
            DBDAttributeBinding binding = bindings[i];
            try {
                Object cellValue = binding.getValueHandler().fetchValueObject(
                        resultSet.getSession(),
                        resultSet,
                        binding.getMetaAttribute(),
                        i);
                if (propertyMap != null && !propertyMap.isEmpty() && propertyMap.containsKey(binding.getLabel().toLowerCase(Locale.ROOT))) {
                    object.put(propertyMap.get(binding.getLabel().toLowerCase(Locale.ROOT)), cellValue);
                } else {
                    object.put(binding.getLabel().toLowerCase(Locale.ROOT), cellValue);
                }
            } catch (Throwable e) {
                if (propertyMap != null && !propertyMap.isEmpty() && propertyMap.containsKey(binding.getLabel().toLowerCase(Locale.ROOT))) {
                    object.putIfAbsent(propertyMap.get(binding.getLabel().toLowerCase(Locale.ROOT)), new DBDValueError(e));
                } else {
                    object.putIfAbsent(binding.getLabel().toLowerCase(Locale.ROOT), new DBDValueError(e));
                }
            }
        }

        return object;
    }
}
