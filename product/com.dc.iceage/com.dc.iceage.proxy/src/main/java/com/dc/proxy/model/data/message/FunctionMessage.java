package com.dc.proxy.model.data.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("函数消息")
public class FunctionMessage extends SchemaMessage {

    @ApiModelProperty(value = "函数名", example = "demo_function")
    private String functionNamePattern;

}
