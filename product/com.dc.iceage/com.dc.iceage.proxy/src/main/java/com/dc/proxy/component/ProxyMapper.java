package com.dc.proxy.component;

import com.dc.repository.mysql.model.DcAccountObjPrivs;
import com.dc.repository.mysql.model.DcDbResource;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ProxyMapper {

    ProxyMapper INSTANCE = Mappers.getMapper(ProxyMapper.class);

    @Mapping(target = "instance_name", source = "resource_name")
    DatabaseConnectionDto toDatabaseConnectionDto(DcDbResource dcDbResource);

    DcAccountObjPrivs copy(DcAccountObjPrivs dcAccountObjPrivs);

}
