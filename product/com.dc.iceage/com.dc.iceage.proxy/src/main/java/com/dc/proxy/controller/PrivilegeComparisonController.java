package com.dc.proxy.controller;

import com.dc.proxy.model.data.message.PrivilegeModelMessage;
import com.dc.proxy.model.data.message.RealPrivilegeMessage;
import com.dc.proxy.service.GenPrivilegeSqlService;
import com.dc.springboot.core.model.data.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api("权限比对")
@RequestMapping("/privilege-comparison")
public class PrivilegeComparisonController {

    @Resource
    private GenPrivilegeSqlService genPrivilegeSqlService;

    @ApiOperation("生成实时权限的sql")
    @PostMapping(value = "/gen-for-real")
    public Result<List<String>> genSqlForRealPrivilege(@RequestBody @Valid RealPrivilegeMessage message) {
        return Result.success(genPrivilegeSqlService.genSqlForReal(message));
    }

    @ApiOperation("生成权限模型的sql")
    @PostMapping(value = "/gen-for-model")
    public Result<List<String>> genSqlForPrivilegeModel(@RequestBody @Valid PrivilegeModelMessage message) {
        return Result.success(genPrivilegeSqlService.genSqlForModel(message));
    }
}
