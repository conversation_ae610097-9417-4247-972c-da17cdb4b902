package com.dc.proxy.controller;

import com.dc.repository.redis.client.DataSourceClient;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.database.DataSourceMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "数据源控制器")
@Slf4j
@RequestMapping("/data-source")
public class DataSourceController {

    @Resource
    private DataSourceClient dataSourceClient;

    @ApiOperation("刷新数据源")
    @PostMapping(value = "/refresh")
    public Result<Object> refresh(@RequestBody @Valid DataSourceMessage message) {
        dataSourceClient.refresh(message);
        return Result.success();
    }

    @ApiOperation("关闭数据源")
    @PostMapping(value = "/close")
    public Result<Object> close(@RequestBody @Valid DataSourceMessage message) {
        dataSourceClient.close(message);
        return Result.success();
    }

}
