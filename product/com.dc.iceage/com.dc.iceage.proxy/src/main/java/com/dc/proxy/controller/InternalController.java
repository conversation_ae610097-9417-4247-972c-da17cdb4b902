package com.dc.proxy.controller;

import com.dc.proxy.model.data.ExplainDataResult;
import com.dc.proxy.model.data.RedisKeysResult;
import com.dc.proxy.model.data.message.HiveMetaDataMessage;
import com.dc.proxy.model.data.message.ProcedureDdlMessage;
import com.dc.proxy.model.data.message.RedisKeysMessage;
import com.dc.proxy.model.result.CatalogSchemaResult;
import com.dc.proxy.model.result.DatabaseResult;
import com.dc.proxy.model.result.HiveResult;
import com.dc.proxy.service.DatabaseService;
import com.dc.proxy.service.ExecuteService;
import com.dc.proxy.service.MetaDataService;
import com.dc.springboot.core.model.data.Result;
import com.dc.springboot.core.model.data.ResultData;
import com.dc.springboot.core.model.data.RowsData;
import com.dc.springboot.core.model.database.*;
import com.dc.springboot.core.model.execution.SqlListExecuteMessage;
import com.dc.summer.model.sql.SqlFieldData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


@Api(tags = "内部控制器")
@Slf4j
@RequestMapping("/internal")
public class InternalController {

    @Resource
    private ExecuteService executeService;

    @Resource
    private MetaDataService metaDataService;

    @Resource
    private DatabaseService databaseService;

    @ApiOperation("执行")
    @PostMapping(value = "/execute")
    public Result<List<RowsData>> execute(@RequestBody @Valid SqlListExecuteMessage message) {
        return Result.success(executeService.execute(message));
    }

    @ApiOperation("多结果集-执行")
    @PostMapping(value = "/multiple-execute")
    public Result<List<ResultData>> multipleExecute(@RequestBody @Valid SqlListExecuteMessage message) {
        return Result.success(executeService.multipleExecute(message));
    }

    @ApiOperation("执行计划")
    @PostMapping(value = "/explain")
    public Result<List<ExplainDataResult>> explain(@RequestBody @Valid SqlListExecuteMessage message) {
        return Result.success(executeService.explain(message));
    }

    @ApiOperation("hive metadata")
    @PostMapping(value = "/metadata")
    public Result<HiveResult> hiveMetadata(@RequestBody @Valid HiveMetaDataMessage message) {
        return Result.success(metaDataService.hiveMetadata(message));
    }

    @ApiOperation("存储过程ddl")
    @PostMapping(value = "/procedure-ddl")
    public Result<String> procedureDdl(@RequestBody @Valid ProcedureDdlMessage message) {
        return Result.success(databaseService.procedureDdl(message));
    }

    @ApiOperation("结构对比")
    @PostMapping(value = "/struct-compare")
    public Result<List<StructCompareInfo>> structCompare(@RequestBody @Valid StructCompareMessage message) {
        return Result.success(databaseService.structCompare(message));
    }

    @ApiOperation("redis keys")
    @PostMapping(value = "/redis-keys")
    public Result<RedisKeysResult> redisKeys(@RequestBody @Valid RedisKeysMessage message) {
        return Result.success(databaseService.getKeys(message));
    }

    @ApiOperation("查询schema信息")
    @PostMapping(value = "/schema-infos")
    public Result<List<SchemaInfo>> schemaInfos(@RequestBody @Valid ConnectionMessage message) {
        return Result.success(databaseService.getSchemas(message));
    }

    @ApiOperation("查询schema信息")
    @PostMapping(value = "/get-schema")
    public Result<List<CatalogSchemaResult>> getSchema(@RequestBody @Valid ConnectionMessage message) {
        return Result.success(metaDataService.getSchemas(message));
    }

    @ApiOperation("获取数据库版本")
    @PostMapping(value = "/get-version")
    public Result<String> getVersion(@RequestBody @Valid ConnectionMessage message) {
        return Result.success(metaDataService.getVersion(message));
    }

    @ApiOperation("获取database信息")
    @PostMapping(value = "/get-database")
    public Result<List<DatabaseResult>> getDatabase(@RequestBody @Valid ConnectionMessage message) {
        return Result.success(databaseService.getDatabase(message));
    }

    @ApiOperation("获取表字段信息")
    @PostMapping(value = "/table-fields")
    public Result<List<SqlFieldData>> tableFields(@RequestBody @Valid TableFieldsMessage message) {
        return Result.success(metaDataService.getTableFields(message));
    }

    @ApiOperation("拆分schema 权限")
    @PostMapping(value = "/spilt-schema-privileges")
    public Result<String> spiltSchemaPrivileges(@RequestBody @Valid ResourceConnectionMessage message) {
        return Result.success(databaseService.splitSchemaPrivilege(message).toString());
    }

    @ApiOperation("获取当前资源目录对象")
    @PostMapping(value = "/get-directories")
    public Result<List<String>> getDirectories(@RequestBody @Valid ConnectionMessage message) {
        return Result.success(databaseService.getDirectories(message));
    }

    @ApiOperation("获取当前对象的类型")
    @PostMapping(value = "/get-directory-object")
    public Result<List<ResourceObject>> getDirectoryObject(@RequestBody @Valid ObjectTypeMessage message) {
        return Result.success(databaseService.getDirectoryObject(message));
    }

}
