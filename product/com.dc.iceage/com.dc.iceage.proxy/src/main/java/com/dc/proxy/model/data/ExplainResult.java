package com.dc.proxy.model.data;

import com.dc.proxy.model.type.IntroduceType;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ExplainResult {

    private Integer id;

    private String filter;

    private String plan_name;

    private String cost;

    private String row_nums;

    private String bytes;

    private String introduce;

    private String pid;

    private List<ExplainResult> children = new ArrayList<>();

    public static List<ExplainResult> parse(String resultObject) {
        String[] resultList = resultObject.split("\\r?\\n");
        List<ExplainResult> explainResults = new ArrayList<>();
        for (String resultTemp : resultList) {
            if (resultTemp.contains("#")) {
                ExplainResult explainResult = new ExplainResult();
                explainResult.setId(resultTemp.substring(0,resultTemp.indexOf("#")).length());
                if (resultTemp.contains(";") && resultTemp.indexOf(";")+1 < resultTemp.length()-1) {
                    explainResult.setFilter(resultTemp.substring(resultTemp.indexOf(";")+1));
                } else {
                    explainResult.setFilter("");
                }
                explainResult.setPlan_name(resultTemp.substring(resultTemp.indexOf("#")+1,resultTemp.indexOf(":")));
                String nums = resultTemp.substring(resultTemp.indexOf("[")+1,resultTemp.indexOf("]")).trim();
                String[] numList = nums.split(",");
                explainResult.setCost(numList[0]);
                explainResult.setRow_nums(numList[1]);
                explainResult.setBytes(numList[2]);
                String introduceType = explainResult.getPlan_name();
                introduceType = introduceType.substring(0,introduceType.length()-1);
                explainResult.setIntroduce(IntroduceType.getValueByName(introduceType) == null ? explainResult.getPlan_name() : IntroduceType.getValueByName(introduceType));
                explainResults.add(explainResult);
            }
        }
        if (explainResults.size() > 0) {
            List<ExplainResult> returnResult = new ArrayList<>();
            ExplainResult explainResult = explainResults.get(0);
            int startNum = explainResult.getId();
            for (ExplainResult resultOutside : explainResults) {
                if (resultOutside.getId().equals(startNum)) {
                    returnResult.add(resultOutside);
                }
                List<ExplainResult> children = new ArrayList<>();
                for (ExplainResult resultInside : explainResults) {
                    if (resultOutside.getId().equals(resultInside.getId()-2)) {
                        children.add(resultInside);
                    }
                }
                resultOutside.setChildren(children);
            }
            return returnResult;
        }
        return null;
    }

}
