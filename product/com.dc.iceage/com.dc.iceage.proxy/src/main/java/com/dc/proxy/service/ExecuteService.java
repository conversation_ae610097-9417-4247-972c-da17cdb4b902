package com.dc.proxy.service;

import com.dc.proxy.model.data.ExplainDataResult;
import com.dc.proxy.model.data.ExplainResult;
import com.dc.springboot.core.model.data.ResultData;
import com.dc.springboot.core.model.data.RowsData;
import com.dc.springboot.core.model.execution.SqlListExecuteMessage;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;

import java.util.List;

public interface ExecuteService {

    /**
     * 每条SQL单结果集执行
     */
    List<RowsData> execute(SqlListExecuteMessage message);

    /**
     * 每条SQL多结果集执行
     */
    List<ResultData> multipleExecute(SqlListExecuteMessage message);

    /**
     * 获取达梦执行计划
     */
    List<ExplainDataResult> explain(SqlListExecuteMessage message);

    WebSQLScriptInfo parseScript(ParseScriptMessage message);

}
