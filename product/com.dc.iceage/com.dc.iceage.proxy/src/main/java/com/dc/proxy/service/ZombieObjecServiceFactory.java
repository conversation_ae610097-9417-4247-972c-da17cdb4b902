package com.dc.proxy.service;

import com.dc.springboot.core.model.database.ConnectionMessage;
import com.dc.type.DatabaseType;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ZombieObjecServiceFactory {

    private final Map<DatabaseType, ZombieObjectService> zombieObjecServiceMap;

    @Autowired
    public ZombieObjecServiceFactory(List<ZombieObjectService> zombieObjecServices) {
        zombieObjecServiceMap = Maps.uniqueIndex(zombieObjecServices, ZombieObjectService::getDataBaseType);
    }

    public void executeZombieObjecService(ConnectionMessage connectionMessage) {
        DatabaseType databaseType = DatabaseType.of(connectionMessage.getConnectionConfig().getDatabaseType());
        ZombieObjectService zombieObjectService = zombieObjecServiceMap.get(databaseType);
        if (zombieObjectService != null) {
            zombieObjectService.updateDcMetaData(connectionMessage);
        } else {
            throw new IllegalArgumentException("No ZombieObjectService found for type: " + databaseType.getName());
        }
    }
}
