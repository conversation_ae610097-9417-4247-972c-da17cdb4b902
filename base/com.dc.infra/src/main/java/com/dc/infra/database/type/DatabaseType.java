
package com.dc.infra.database.type;

import com.dc.infra.spi.TypedSPI;
import com.dc.infra.spi.annotation.SingletonSPI;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collection;
import java.util.Optional;

/**
 * Database type.
 */
@SingletonSPI
public interface DatabaseType extends TypedSPI {
    
    /**
     * Get JDBC URL prefixes.
     * 
     * @return prefixes of JDBC URL
     */
    Collection<String> getJdbcUrlPrefixes();
    
    /**
     * Get trunk database type.
     * 
     * @return trunk database type
     */
    default Optional<DatabaseType> getTrunkDatabaseType() {
        return Optional.empty();
    }

    @Override
    default Constant getType() {
        return null;
    }

    @AllArgsConstructor
    @Getter
    enum Constant {
        ORACLE("Oracle"),
        MYSQL("MySQL"),
        SQL_SERVER("SQLServer"),
        INFORMIX("Informix"),
        PG_SQL("PostgreSQL"),
        GAUSSDB("GaussDB"),
        CLICKHOUSE("ClickHouse"),
        HIVE("Hive"),
        IMPALA("Impala"),
        SPARK("Spark"),
        HETU("Hetu"),
        MONGO_DB("MongoDB"),
        REDIS("Redis"),
        DB2("Db2"),
        DM("DM"),
        HANA("Hana"),
        VERTICA("Vertica"),
        KINGBASE_PG("KingBase_PG"),
        KINGBASE_ORACLE("KingBase_Oracle"),
        GREENPLUM("Greenplum"),
        RASESQL("RASESQL"),
        TD_PG("TD_PG"),
        TD_MYSQL("TD_MySQL"),
        ANALYTICDB_MYSQL("AnalyticDB_MySQL"),
        MYCAT("Mycat"),
        OB_MYSQL("OB_MySQL"),
        TIDB("TiDB"),
        MARIADB("MariaDB"),
        GAUSSDB_PG("GaussDB_PG"),
        GAUSSDB_M("GaussDB_M"),
        VASTBASE_G100("Vastbase_G100"),
        ;

        final String name;

        public static Constant fromName(String name) {
            for (Constant constant : Constant.values()) {
                if (constant.name.equalsIgnoreCase(name)) {
                    return constant;
                }
            }
            throw new IllegalArgumentException("No enum constant with name: " + name);
        }

    }
}
