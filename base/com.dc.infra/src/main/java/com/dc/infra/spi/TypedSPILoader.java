
package com.dc.infra.spi;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.exception.ServiceProviderNotFoundException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Optional;
import java.util.Properties;

/**
 * Typed SPI loader.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TypedSPILoader {
    
    /**
     * Find service.
     * 
     * @param serviceInterface typed SPI service interface
     * @param type type
     * @param <T> SPI class type
     * @return found service
     */
    public static <T extends TypedSPI> Optional<T> findService(final Class<T> serviceInterface, final Object type) {
        return findService(serviceInterface, type, new Properties());
    }
    
    /**
     * Find service.
     * 
     * @param serviceInterface typed SPI service interface
     * @param type type
     * @param props properties
     * @param <T> SPI class type
     * @return found service
     */
    public static <T extends TypedSPI> Optional<T> findService(final Class<T> serviceInterface, final Object type, final Properties props) {
        if (null == type) {
            return findDefaultService(serviceInterface);
        }
        for (T each : ShardingSphereServiceLoader.getServiceInstances(serviceInterface)) {
            if (matchesType(type, each)) {
                each.init(null == props ? new Properties() : convertToStringTypedProperties(props));
                return Optional.of(each);
            }
        }
        return Optional.empty();
    }
    
    private static <T extends TypedSPI> Optional<T> findDefaultService(final Class<T> serviceInterface) {
        for (T each : ShardingSphereServiceLoader.getServiceInstances(serviceInterface)) {
            if (!each.isDefault()) {
                continue;
            }
            each.init(new Properties());
            return Optional.of(each);
        }
        return Optional.empty();
    }
    
    private static Properties convertToStringTypedProperties(final Properties props) {
        if (props.isEmpty()) {
            return props;
        }
        Properties result = new Properties();
        props.forEach((key, value) -> result.setProperty(key.toString(), null == value ? null : value.toString()));
        return result;
    }
    
    /**
     * Get service.
     * 
     * @param serviceInterface typed SPI service interface
     * @param type type
     * @param <T> SPI class type
     * @return service
     */
    public static <T extends TypedSPI> T getService(final Class<T> serviceInterface, final Object type) {
        return getService(serviceInterface, type, new Properties());
    }
    
    /**
     * Get service.
     * 
     * @param serviceInterface typed SPI service interface
     * @param type type
     * @param props properties
     * @param <T> SPI class type
     * @return service
     */
    public static <T extends TypedSPI> T getService(final Class<T> serviceInterface, final Object type, final Properties props) {
        return findService(serviceInterface, type, props).orElseGet(() -> findService(serviceInterface, null, props).orElseThrow(() -> new ServiceProviderNotFoundException(serviceInterface, type)));
    }
    
    /**
     * Check service.
     * 
     * @param serviceInterface typed SPI service interface
     * @param type type
     * @param props properties
     * @param <T> SPI class type
     * @throws ServiceProviderNotFoundException service provider not found server exception
     */
    public static <T extends TypedSPI> void checkService(final Class<T> serviceInterface, final Object type, final Properties props) {
        for (T each : ShardingSphereServiceLoader.getServiceInstances(serviceInterface)) {
            if (matchesType(type, each)) {
                each.init(null == props ? new Properties() : convertToStringTypedProperties(props));
                return;
            }
        }
        throw new ServiceProviderNotFoundException(serviceInterface, type);
    }
    
    private static boolean matchesType(final Object type, final TypedSPI instance) {
        Object instanceType = instance.getType();
        if (null == instanceType) {
            return false;
        }
        if (instanceType instanceof DatabaseType.Constant && type instanceof String) {
            return instanceType == DatabaseType.Constant.fromName(type.toString());
        }
        if (instanceType instanceof DatabaseType.Constant && type instanceof DatabaseType.Constant) {
            return instanceType == type || instance.getTypeAliases().contains(type);
        }
        if (instanceType instanceof String && type instanceof String) {
            return instanceType.toString().equalsIgnoreCase(type.toString()) || instance.getTypeAliases().contains(type);
        }
        return instanceType.equals(type) || instance.getTypeAliases().contains(type);
    }
}
