package com.dc.parser.ext.gaussdb.context.segment.select.projection.extractor;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.context.segment.select.projection.extractor.DialectProjectionIdentifierExtractor;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.FunctionSegment;
import com.dc.parser.model.segment.dml.item.ExpressionProjectionSegment;
import com.dc.parser.model.segment.dml.item.SubqueryProjectionSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

/**
 * Projection identifier extractor for openGauss.
 */
public final class GaussDBProjectionIdentifierExtractor implements DialectProjectionIdentifierExtractor {

    @Override
    public String getIdentifierValue(final IdentifierValue identifierValue) {
        return identifierValue.getValue().toLowerCase();
    }

    @Override
    public String getColumnNameFromFunction(final String functionName, final String functionExpression) {
        return functionName.toLowerCase();
    }

    @Override
    public String getColumnNameFromExpression(final ExpressionSegment expressionSegment) {
        return expressionSegment instanceof ExpressionProjectionSegment && ((ExpressionProjectionSegment) expressionSegment).getExpr() instanceof FunctionSegment
                ? ((FunctionSegment) ((ExpressionProjectionSegment) expressionSegment).getExpr()).getFunctionName()
                : "?column?";
    }

    @Override
    public String getColumnNameFromSubquery(final SubqueryProjectionSegment subquerySegment) {
        // TODO support subquery projection
        return subquerySegment.getText();
    }

    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.GAUSSDB;
    }
}
