
package com.dc.parser.exec.sql;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.exec.cache.SQLStatementCacheBuilder;
import com.dc.parser.model.engine.CacheOption;
import com.dc.parser.model.statement.SQLStatement;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.Getter;

/**
 * SQL statement parser engine.
 */
public final class SQLStatementParserEngine {
    
    private final SQLStatementParserExecutor sqlStatementParserExecutor;
    
    private final LoadingCache<String, SQLStatement> sqlStatementCache;
    
    @Getter
    private final CacheOption sqlStatementCacheOption;
    
    @Getter
    private final CacheOption parseTreeCacheOption;
    
    public SQLStatementParserEngine(final DatabaseType databaseType, final CacheOption sqlStatementCacheOption, final CacheOption parseTreeCacheOption) {
        sqlStatementParserExecutor = new SQLStatementParserExecutor(databaseType, parseTreeCacheOption);
        sqlStatementCache = SQLStatementCacheBuilder.build(databaseType, sqlStatementCacheOption, parseTreeCacheOption);
        this.sqlStatementCacheOption = sqlStatementCacheOption;
        this.parseTreeCacheOption = parseTreeCacheOption;
    }
    
    /**
     * Parse to SQL statement.
     *
     * @param sql SQL to be parsed
     * @param useCache whether to use cache
     * @return SQL statement
     */
    public SQLStatement parse(final String sql, final boolean useCache) {
        return useCache ? sqlStatementCache.get(sql) : sqlStatementParserExecutor.parse(sql);
    }
}
