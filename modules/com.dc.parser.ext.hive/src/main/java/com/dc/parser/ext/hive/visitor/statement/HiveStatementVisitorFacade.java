package com.dc.parser.ext.hive.visitor.statement;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.ext.hive.visitor.statement.type.HiveDDLStatementVisitor;
import com.dc.parser.model.api.visitor.statement.type.DALStatementVisitor;
import com.dc.parser.model.api.visitor.statement.type.DCLStatementVisitor;
import com.dc.parser.model.api.visitor.statement.type.DDLStatementVisitor;
import com.dc.parser.model.api.visitor.statement.type.DMLStatementVisitor;
import com.dc.parser.model.api.visitor.statement.type.RLStatementVisitor;
import com.dc.parser.model.api.visitor.statement.type.TCLStatementVisitor;
import com.dc.parser.model.api.visitor.statement.type.*;
import com.dc.parser.ext.hive.visitor.statement.type.HiveDMLStatementVisitor;
import com.dc.parser.model.spi.SQLStatementVisitorFacade;

/**
 * Statement visitor facade for hive.
 */
public final class HiveStatementVisitorFacade implements SQLStatementVisitorFacade {
    
    @Override
    public Class<? extends DMLStatementVisitor> getDMLVisitorClass() {
        return HiveDMLStatementVisitor.class;
    }
    
    @Override
    public Class<? extends DDLStatementVisitor> getDDLVisitorClass() {
        return HiveDDLStatementVisitor.class;
    }
    
    @Override
    public Class<? extends TCLStatementVisitor> getTCLVisitorClass() {
        throw new UnsupportedOperationException("");
    }
    
    @Override
    public Class<? extends DCLStatementVisitor> getDCLVisitorClass() {
        throw new UnsupportedOperationException("");
    }
    
    @Override
    public Class<? extends DALStatementVisitor> getDALVisitorClass() {
        throw new UnsupportedOperationException("");
    }
    
    @Override
    public Class<? extends RLStatementVisitor> getRLVisitorClass() {
        throw new UnsupportedOperationException("");
    }

    @Override
    public Class<? extends BatchStatementVisitor> getBatchVisitorClass() {
        return null;
    }

    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.HIVE;
    }
}
