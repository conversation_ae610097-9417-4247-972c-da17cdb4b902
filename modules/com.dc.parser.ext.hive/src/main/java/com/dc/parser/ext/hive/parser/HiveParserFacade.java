package com.dc.parser.ext.hive.parser;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.api.parser.SQLLexer;
import com.dc.parser.model.api.parser.SQLParser;
import com.dc.parser.model.spi.DialectSQLParserFacade;

/**
 * SQL parser facade for hive.
 */
public final class HiveParserFacade implements DialectSQLParserFacade {
    
    @Override
    public Class<? extends SQLLexer> getLexerClass() {
        return HiveLexer.class;
    }
    
    @Override
    public Class<? extends SQLParser> getParserClass() {
        return HiveParser.class;
    }
    
    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.HIVE;
    }
}
