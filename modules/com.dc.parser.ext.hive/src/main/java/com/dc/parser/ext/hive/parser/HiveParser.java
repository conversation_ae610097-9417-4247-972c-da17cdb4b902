package com.dc.parser.ext.hive.parser;

import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.TokenStream;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.parser.SQLParser;
import com.dc.parser.ext.hive.parser.autogen.HiveStatementParser;
import com.dc.parser.model.engine.ParseASTNode;

/**
 * SQL parser for hive.
 */
public final class HiveParser extends HiveStatementParser implements SQLParser {
    
    public HiveParser(final TokenStream input) {
        super(input);
    }
    
    @Override
    public ASTNode parse() {
        return new ParseASTNode(root(), (CommonTokenStream) getTokenStream());
    }
}
