package com.dc.parser.test.clickhouse.external;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.test.AbstractExternalParserIT;

/**
 * Clickhouse 解析器完整性动态集成测试。
 *
 * <p>本测试通过 Clickhouse 官方的测试集来全面验证解析器的正确性。
 * 所有的测试逻辑均由 {@link AbstractExternalParserIT} 基类提供。</p>
 *
 * <h2>测试准备</h2>
 * <p>请将 Clickhouse 测试集仓库克隆到本地，并确保下面的路径正确无误。</p>
 *
 * <p>测试集仓库地址: <a href="https://codeup.aliyun.com/614ac62ae43534781c03c36e/clickhouse-test-sql.git">https://codeup.aliyun.com/614ac62ae43534781c03c36e/clickhouse-test-sql.git</a></p>
 */
public class ExternalClickhouseParserIT extends AbstractExternalParserIT {

    @Override
    protected String getTestsPath() {
        return "/Users/<USER>/Downloads/clickhouse-test-sql";
    }

    @Override
    protected DatabaseType.Constant getDatabaseTypeConstant() {
        return DatabaseType.Constant.CLICKHOUSE;
    }
}