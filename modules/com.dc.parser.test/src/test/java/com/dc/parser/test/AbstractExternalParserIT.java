package com.dc.parser.test;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.exec.sql.SQLStatementParserEngine;
import com.dc.parser.exec.sql.SQLStatementParserEngineFactory;
import com.dc.parser.model.engine.CacheOption;
import com.dc.parser.model.statement.ErrorStatement;
import com.dc.parser.model.statement.SQLStatement;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DynamicContainer;
import org.junit.jupiter.api.DynamicTest;
import org.junit.jupiter.api.TestFactory;
import org.junit.jupiter.api.function.Executable;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 外部 SQL 解析器完整性动态集成测试的抽象基类。
 *
 * <p>本类提供了通过扫描指定目录下的 .sql 文件，并为其中每一条 SQL 语句
 * 动态生成 JUnit 5 测试的核心逻辑。子类只需提供具体的测试文件路径和
 * 数据库类型即可复用整个测试流程。</p>
 *
 * @see TestFactory
 * @see DynamicTest
 * @see DynamicContainer
 */
public abstract class AbstractExternalParserIT {

    private SQLStatementParserEngine sqlStatementParserEngine;

    /**
     * 子类必须实现此方法，以提供具体的 SQL 测试文件存放路径。
     *
     * @return SQL 测试文件的根目录路径。
     */
    protected abstract String getTestsPath();

    /**
     * 子类必须实现此方法，以提供具体的数据库类型常量。
     *
     * @return 数据库类型，例如 {@code DatabaseType.Constant.CLICKHOUSE}。
     */
    protected abstract DatabaseType.Constant getDatabaseTypeConstant();

    /**
     * 动态测试工厂，这是 JUnit 5 测试的入口点。
     * 它会初始化解析器引擎，然后扫描文件并为每个文件创建测试容器。
     *
     * @return 动态测试容器的流。
     * @throws IOException 如果扫描文件时发生 I/O 错误。
     */
    @TestFactory
    Stream<DynamicContainer> validateParserDynamically() throws IOException {
        // 初始化特定数据库的解析器引擎
        initializeParserEngine();

        System.out.printf("动态测试工厂启动 [%s]，正在扫描测试文件...\n", getDatabaseTypeConstant());
        Path rootPath = Paths.get(getTestsPath());
        if (!Files.exists(rootPath)) {
            Assertions.fail("测试目录不存在: " + getTestsPath());
        }

        List<Path> sqlFiles = Files.walk(rootPath)
                .filter(path -> path.toString().endsWith(".sql"))
                .collect(Collectors.toList());

        System.out.printf("发现 %d 个测试文件，开始为每个文件生成测试容器...\n", sqlFiles.size());
        return sqlFiles.stream().map(this::createContainerForFile);
    }

    /**
     * 初始化 SQL 解析器引擎。
     */
    private void initializeParserEngine() {
        if (this.sqlStatementParserEngine == null) {
            DatabaseType databaseType = TypedSPILoader.getService(DatabaseType.class, getDatabaseTypeConstant());
            this.sqlStatementParserEngine = SQLStatementParserEngineFactory.getSQLStatementParserEngine(
                    databaseType,
                    new CacheOption(2000, 65535L),
                    new CacheOption(64, 1024L)
            );
        }
    }

    /**
     * 为单个 .sql 文件创建一个测试容器，其中包含该文件内所有 SQL 语句的动态测试。
     *
     * @param sqlFile SQL 文件的路径
     * @return 一个 DynamicContainer
     */
    private DynamicContainer createContainerForFile(Path sqlFile) {
        String containerDisplayName = sqlFile.getFileName().toString();
        List<DynamicTest> tests = new ArrayList<>();
        try {
            String content = new String(Files.readAllBytes(sqlFile));
            // 使用分号和换行符来切分，更稳健
            String[] statements = content.split("(?m);\\s*$");
            AtomicInteger sqlIndex = new AtomicInteger(1);

            for (String stmt : statements) {
                String sql = stmt.trim();
                if (sql.isEmpty()) {
                    continue;
                }

                String testDisplayName = String.format("SQL %d: %s",
                        sqlIndex.getAndIncrement(),
                        sql.substring(0, Math.min(sql.length(), 70)).replace('\n', ' ') + "...");

                Executable executable = () -> {
                    SQLStatement statement = sqlStatementParserEngine.parse(sql, true);
                    if (statement instanceof ErrorStatement) {
                        ErrorStatement errorStatement = (ErrorStatement) statement;
                        Assertions.fail("SQL 解析失败: " + errorStatement.getErrorMessage() + "\nSQL: " + sql);
                    }
                };
                tests.add(DynamicTest.dynamicTest(testDisplayName, executable));
            }
        } catch (IOException e) {
            tests.add(DynamicTest.dynamicTest("File Read Error", () ->
                    Assertions.fail("无法读取测试文件: " + sqlFile, e)
            ));
        }
        return DynamicContainer.dynamicContainer(containerDisplayName, tests);
    }
}
