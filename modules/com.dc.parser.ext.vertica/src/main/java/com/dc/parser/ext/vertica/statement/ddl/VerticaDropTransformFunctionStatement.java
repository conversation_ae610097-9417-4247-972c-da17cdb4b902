package com.dc.parser.ext.vertica.statement.ddl;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.statement.ddl.DropFunctionStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Vertica drop transform function statement.
 */
@Setter
@Getter
public final class VerticaDropTransformFunctionStatement extends DropFunctionStatement implements VerticaStatement {
    private boolean ifExists;
}
