package com.dc.parser.ext.vertica.statement.ddl;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.statement.ddl.DropIndexStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Vertica drop text index statement.
 */
@Setter
@Getter
public final class VerticaDropTextIndexStatement extends DropIndexStatement implements VerticaStatement {
    private boolean ifExists;
}
