package com.dc.parser.ext.vertica.statement.dml;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.segment.generic.OutputSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.statement.dml.DeleteStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * Vertica delete statement.
 */
@Setter
public final class VerticaDeleteStatement extends DeleteStatement implements VerticaStatement {
    
    private WithSegment withSegment;
    
    private OutputSegment outputSegment;
    
    /**
     * Get with segment.
     *
     * @return with segment.
     */
    public Optional<WithSegment> getWithSegment() {
        return Optional.ofNullable(withSegment);
    }
    
    /**
     * Get output segment.
     *
     * @return output segment.
     */
    public Optional<OutputSegment> getOutputSegment() {
        return Optional.ofNullable(outputSegment);
    }
}
