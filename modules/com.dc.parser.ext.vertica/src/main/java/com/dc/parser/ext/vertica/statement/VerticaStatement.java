package com.dc.parser.ext.vertica.statement;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.model.statement.SQLStatement;

/**
 * Vertica statement.
 */
public interface VerticaStatement extends SQLStatement {
    
    @Override
    default DatabaseType getDatabaseType() {
        return TypedSPILoader.getService(DatabaseType.class, DatabaseType.Constant.VERTICA);
    }
}
