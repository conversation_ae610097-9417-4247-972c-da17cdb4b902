package com.dc.parser.ext.vertica.statement.dml;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dml.CopyStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Vertica copy statement.
 */
@Setter
@Getter
public final class VerticaCopyStatement extends CopyStatement implements VerticaStatement {
    private SimpleTableSegment sourceTable;
}
