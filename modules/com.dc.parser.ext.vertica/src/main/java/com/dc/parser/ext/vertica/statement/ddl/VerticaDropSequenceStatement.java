package com.dc.parser.ext.vertica.statement.ddl;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.segment.ddl.routine.FunctionNameSegment;
import com.dc.parser.model.statement.ddl.DropSequenceStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;

/**
 * Vertica drop sequence statement.
 */
@Setter
@Getter
public final class VerticaDropSequenceStatement extends DropSequenceStatement implements VerticaStatement {
    Collection<FunctionNameSegment> sequenceNameSegments;

}
