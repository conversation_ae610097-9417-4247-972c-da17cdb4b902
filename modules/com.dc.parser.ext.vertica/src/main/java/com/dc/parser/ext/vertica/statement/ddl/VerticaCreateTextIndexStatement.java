package com.dc.parser.ext.vertica.statement.ddl;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.statement.ddl.CreateIndexStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Vertica create text index statement.
 */
@Setter
@Getter
public final class VerticaCreateTextIndexStatement extends CreateIndexStatement implements VerticaStatement {
    private String textField;

    public Optional<String> getTextField() {
        return Optional.ofNullable(textField);
    }
}
