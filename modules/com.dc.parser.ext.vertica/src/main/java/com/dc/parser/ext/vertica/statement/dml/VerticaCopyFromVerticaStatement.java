package com.dc.parser.ext.vertica.statement.dml;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dml.CopyStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;

/**
 * Vertica copy from vertica statement.
 */
@Setter
@Getter
public final class VerticaCopyFromVerticaStatement extends CopyStatement implements VerticaStatement {
    private SimpleTableSegment sourceTable;
    private Collection<ColumnSegment> sourceColumns;

}
