package com.dc.parser.ext.vertica.statement.ddl;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.TruncateStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Vertica truncate table statement.
 */
@Setter
@Getter
public final class VerticaTruncateTableStatement extends TruncateStatement implements VerticaStatement {
    private SimpleTableSegment table;
}
