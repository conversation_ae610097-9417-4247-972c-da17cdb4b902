package com.dc.parser.ext.vertica.statement.dml;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.hint.OptionHintSegment;
import com.dc.parser.model.segment.dml.hint.WithTableHintSegment;
import com.dc.parser.model.segment.dml.merge.MergeWhenAndThenSegment;
import com.dc.parser.model.segment.generic.AliasSegment;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.statement.dml.MergeStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;
import java.util.Optional;

/**
 * vertica merge statement.
 */
@Setter
@Getter
public final class VerticaMergeStatement extends MergeStatement implements VerticaStatement {

    private SelectStatement sourceSelect;
    private AliasSegment sourceAlias;
    private Collection<ExpressionSegment> joinPredicate = new LinkedList<>();


    private WithSegment withSegment;
    
    private WithTableHintSegment withTableHintSegment;
    
    private Collection<IndexSegment> indexes = new LinkedList<>();
    

    
    private OptionHintSegment optionHintSegment;
    
    private Collection<MergeWhenAndThenSegment> whenAndThenSegments = new LinkedList<>();
    
    /**
     * Get with segment.
     *
     * @return with segment.
     */
    public Optional<WithSegment> getWithSegment() {
        return Optional.ofNullable(withSegment);
    }
    
    /**
     * Get with table hint segment.
     *
     * @return with table hint segment.
     */
    public Optional<WithTableHintSegment> getWithTableHintSegment() {
        return Optional.ofNullable(withTableHintSegment);
    }

    /**
     * Get option hint segment.
     *
     * @return option hint segment.
     */
    public Optional<OptionHintSegment> getOptionHintSegment() {
        return Optional.ofNullable(optionHintSegment);
    }
}
