package com.dc.parser.ext.vertica.statement.dml;

import com.dc.parser.ext.vertica.statement.VerticaStatement;
import com.dc.parser.model.segment.dml.hint.WithTableHintSegment;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.Optional;

/**
 * Vertica select statement.
 */
@Setter
@Getter
public final class VerticaSelectStatement extends SelectStatement implements VerticaStatement {
    
    private LimitSegment limit;

    private TableSegment intoSegment;
    
    private WithTableHintSegment withTableHintSegment;

    private SelectStatement intersect;
    private SelectStatement except;
    private boolean forUpdate;
    private Collection<TableSegment> forUpdateTables;

    
    /**
     * Get order by segment.
     *
     * @return order by segment
     */
    public Optional<LimitSegment> getLimit() {
        return Optional.ofNullable(limit);
    }
    
    /**
     * Get into segment.
     *
     * @return into segment
     */
    public Optional<TableSegment> getIntoSegment() {
        return Optional.ofNullable(intoSegment);
    }
    
    /**
     * Get with table hint segment.
     *
     * @return with table hint segment.
     */
    public Optional<WithTableHintSegment> getWithTableHintSegment() {
        return Optional.ofNullable(withTableHintSegment);
    }
}
