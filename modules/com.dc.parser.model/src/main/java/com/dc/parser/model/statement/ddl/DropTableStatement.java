package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Drop table statement.
 */
@Getter
public abstract class DropTableStatement extends AbstractSQLStatement implements DDLStatement {
    
    private final Collection<SimpleTableSegment> tables = new LinkedList<>();

    /**
     * Set if exists.
     *
     * @param ifExists if exists or not
     */
    public void setIfExists(final boolean ifExists) {
    }

    /**
     * Judge whether contains if exists.
     *
     * @return contains contains if exists or not
     */
    public boolean isIfExists() {
        return false;
    }

    /**
     * Set contains cascade.
     *
     * @param containsCascade contains cascade or not
     */
    public void setContainsCascade(final boolean containsCascade) {
    }

    /**
     * Judge whether contains cascade.
     *
     * @return contains cascade or not
     */
    public boolean isContainsCascade() {
        return false;
    }

    public IdentifierValue getRootTableName() {
        return null;
    }
}
