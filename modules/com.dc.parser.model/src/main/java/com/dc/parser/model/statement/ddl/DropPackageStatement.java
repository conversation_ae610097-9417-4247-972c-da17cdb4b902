package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.packages.PackageSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Drop package statement.
 */
@Getter
@Setter
public abstract class DropPackageStatement extends AbstractSQLStatement implements DDLStatement {

    private PackageSegment packageSegment;
}
