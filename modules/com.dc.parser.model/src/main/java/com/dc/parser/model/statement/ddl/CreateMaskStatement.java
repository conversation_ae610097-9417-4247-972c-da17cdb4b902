package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.CaseWhenExpression;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

public abstract class CreateMaskStatement extends AbstractSQLStatement implements DDLStatement {



    public IdentifierValue getMaskName() {
        return null;
    }

    public SimpleTableSegment getTable() {
        return null;
    }

    public IdentifierValue getCorrelationName() {
        return null;
    }

    public ColumnSegment getColumn() {
        return null;
    }

    public CaseWhenExpression getCaseWhenExpression() {
        return null;
    }
}
