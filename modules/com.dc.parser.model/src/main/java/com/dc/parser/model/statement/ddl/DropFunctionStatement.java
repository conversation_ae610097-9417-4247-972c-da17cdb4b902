package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.routine.FunctionNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Drop function statement.
 */
@Getter
@Setter
public abstract class DropFunctionStatement extends AbstractSQLStatement implements DDLStatement {

    private FunctionNameSegment functionName;


    /**
     * Get function name segment.
     *
     * @return function name segment
     */
    public Optional<FunctionNameSegment> getFunctionName() {
        return Optional.ofNullable(functionName);
    }


    public IdentifierValue getSpecificName() {
        return null;
    }
}
