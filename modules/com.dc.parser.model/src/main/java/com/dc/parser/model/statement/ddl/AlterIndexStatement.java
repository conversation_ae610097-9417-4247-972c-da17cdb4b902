package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * Alter index statement.
 */
@Setter
public abstract class AlterIndexStatement extends AbstractSQLStatement implements DDLStatement {
    
    private IndexSegment index;
    
    /**
     * Get index segment.
     *
     * @return index segment
     */
    public Optional<IndexSegment> getIndex() {
        return Optional.ofNullable(index);
    }

    /**
     * Get simple table segment.
     *
     * @return simple table segment
     */
    public Optional<SimpleTableSegment> getSimpleTable() {
        return Optional.empty();
    }

    /**
     * Set simple table segment.
     *
     * @param simpleTableSegment simple table segment
     */
    public void setSimpleTable(final SimpleTableSegment simpleTableSegment) {
    }

    /**
     * Get rename index segment.
     *
     * @return rename index segment
     */
    public Optional<IndexSegment> getRenameIndex() {
        return Optional.empty();
    }
}
