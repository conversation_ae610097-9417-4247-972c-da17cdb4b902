package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Setter
@Getter
public abstract class AlterModelStatement extends AbstractSQLStatement implements DDLStatement{
    private SimpleTableSegment modelName;

    /**
     * Get schemaName.
     *
     * @return schemaName
     */
    public Optional<SimpleTableSegment> getModelName() {
        return Optional.ofNullable(modelName);
    }
}
