package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.ObjectNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

public abstract class CreateMethodStatement extends AbstractSQLStatement implements DDLStatement {



    public IdentifierValue getMethodName() {
        return null;
    }

    public ObjectNameSegment getTypeName() {
        return null;
    }

}
