package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.DataTypeSegment;
import com.dc.parser.model.segment.generic.ObjectNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

public abstract class CreateVariableStatement extends AbstractSQLStatement implements DDLStatement {


    public ObjectNameSegment getVariableName() {
        return null;
    }

    public DataTypeSegment getDataType() {
        return null;
    }
}
