package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * UnDropTable statement.
 */
@Getter
@Setter
public abstract class UnDropTableStatement extends AbstractSQLStatement implements DDLStatement {
    private final Collection<SimpleTableSegment> tables = new LinkedList<>();

}