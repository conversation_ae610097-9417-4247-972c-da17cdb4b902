package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.trigger.TriggerNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Drop trigger statement.
 */
@Getter
@Setter
public abstract class DropTriggerStatement extends AbstractSQLStatement implements DDLStatement {

    private TriggerNameSegment triggerNameSegment;
}
