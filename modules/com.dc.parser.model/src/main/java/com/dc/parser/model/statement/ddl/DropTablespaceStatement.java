package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.tablespace.TablespaceSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Drop tablespace statement.
 */
@Getter
public abstract class DropTablespaceStatement extends AbstractSQLStatement implements DDLStatement {

    public Collection<TablespaceSegment> tablespaceSegments = new LinkedList<>();
}
