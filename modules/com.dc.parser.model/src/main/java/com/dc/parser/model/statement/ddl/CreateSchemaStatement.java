
package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Create schema statement.
 */
@Getter
@Setter
public abstract class CreateSchemaStatement extends AbstractSQLStatement implements DDLStatement {
    
    private IdentifierValue schemaName;
    
    /**
     * Get schema name.
     * 
     * @return schema name
     */
    public Optional<IdentifierValue> getSchemaName() {
        return Optional.ofNullable(schemaName);
    }

    /**
     * Get username.
     *
     * @return username
     */
    public Optional<IdentifierValue> getUsername() {
        return Optional.empty();
    }
}
