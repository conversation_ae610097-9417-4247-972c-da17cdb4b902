package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.plsql.ProcedureCallNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.List;
import java.util.Optional;

/**
 * PLSQL block statement.
 */
public abstract class PLSQLBlockStatement extends AbstractSQLStatement implements DDLStatement {

    /**
     * Get procedure call name segments.
     *
     * @return procedure call name segments
     */
    public Optional<List<ProcedureCallNameSegment>> getProcedureCallNameSegments() {
        return Optional.empty();
    }

    @Override
    public int getParameterCount() {
        return 0;
    }
}
