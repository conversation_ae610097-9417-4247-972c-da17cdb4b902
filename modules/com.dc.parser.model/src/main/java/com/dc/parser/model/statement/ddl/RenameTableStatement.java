
package com.dc.parser.model.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.ddl.table.RenameTableDefinitionSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Rename table statement.
 */
@Getter
@Setter
public abstract class RenameTableStatement extends AbstractSQLStatement implements DDLStatement {
    
    private final Collection<RenameTableDefinitionSegment> renameTables = new LinkedList<>();
}
