package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.session.ParameterSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Collections;
import java.util.List;

/**
 * Alter session statement.
 */
public abstract class AlterSessionStatement extends AbstractSQLStatement implements DDLStatement {

    public List<ParameterSegment> getParameters() {
        return Collections.emptyList();
    }
}
