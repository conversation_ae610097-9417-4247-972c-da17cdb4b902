package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.synonym.SynonymNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Create synonym statement.
 */
@Getter
@Setter
public abstract class CreateSynonymStatement extends AbstractSQLStatement implements DDLStatement {

    private SynonymNameSegment synonymNameSegment;
}
