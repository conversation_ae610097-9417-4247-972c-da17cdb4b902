package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.routine.ProcedureNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Alter procedure statement.
 */
@Getter
@Setter
public abstract class AlterProcedureStatement extends AbstractSQLStatement implements DDLStatement {

    private ProcedureNameSegment procedureNameSegment;
}
