package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.trigger.TriggerNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Create trigger statement.
 */
@Getter
@Setter
public abstract class CreateTriggerStatement extends AbstractSQLStatement implements DDLStatement {

    private TriggerNameSegment triggerNameSegment;

}
