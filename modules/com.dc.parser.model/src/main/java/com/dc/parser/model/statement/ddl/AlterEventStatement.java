package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.routine.EventNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Alter event statement.
 */
@Setter
@Getter
public abstract class AlterEventStatement extends AbstractSQLStatement implements DDLStatement {

    private EventNameSegment eventNameSegment;
}
