package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.ObjectNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Optional;

public abstract class DropVariableStatement extends AbstractSQLStatement implements DDLStatement {

    public Optional<ObjectNameSegment> getVarName() {
        return Optional.empty();
    }

    public boolean isTemporary() {
        return false;
    }

    public void setTemporary(boolean temporary) {
    }

    public Optional<String> getVariableKeyword() {
        return Optional.empty();
    }

    public void setVariableKeyword(String variableKeyword) {
    }

    public boolean isIfExists() {
        return false;
    }

    public void setIfExists(boolean ifExists) {
    }

    public Optional<IdentifierValue> getVariableName() {
        return Optional.empty();
    }

    public void setVariableName(IdentifierValue variableName) {
    }
}
