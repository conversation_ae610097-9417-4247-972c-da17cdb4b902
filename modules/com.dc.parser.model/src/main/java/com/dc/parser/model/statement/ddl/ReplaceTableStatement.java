package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.bucket.BucketSegment;
import com.dc.parser.model.segment.ddl.cluster.ClusterBySegment;
import com.dc.parser.model.segment.ddl.cluster.SkewSegment;
import com.dc.parser.model.segment.ddl.collation.CollationSegment;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.partition.PartitionSegment;
import com.dc.parser.model.segment.generic.CommentSegment;
import com.dc.parser.model.segment.generic.LocationSegment;
import com.dc.parser.model.segment.generic.RowFormatSegment;
import com.dc.parser.model.segment.generic.StoredSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.*;

public abstract class ReplaceTableStatement extends AbstractSQLStatement implements DDLStatement {

    public boolean isCreateOr() {
        return false;
    }

    public void setCreateOr(boolean createOr) {
    }

    public Optional<SimpleTableSegment> getTable() {
        return Optional.empty();
    }

    public void setTable(SimpleTableSegment table) {
    }

    public Collection<ColumnDefinitionSegment> getColumnDefinitionSegments() {
        return Collections.emptyList();
    }

    public Collection<ConstraintDefinitionSegment> getConstraintDefinitionSegments() {
        return Collections.emptyList();
    }

    public Optional<SimpleTableSegment> getTableProvider() {
        return Optional.empty();
    }

    public void setTableProvider(SimpleTableSegment tableProvider) {
    }

    public Map<String, String> getOptions() {
        return Collections.emptyMap();
    }

    public void setOptions(Map<String, String> options) {
    }

    public List<PartitionSegment> getPartitionSegments() {
        return Collections.emptyList();
    }

    public List<SkewSegment> getSkewSegments() {
        return Collections.emptyList();
    }

    public void setSkewSegments(List<SkewSegment> skewSegments) {
    }

    public List<ClusterBySegment> getClusterBySegments() {
        return Collections.emptyList();
    }

    public void setClusterBySegments(List<ClusterBySegment> clusterBySegments) {
    }

    public List<BucketSegment> getBucketSegments() {
        return Collections.emptyList();
    }

    public void setBucketSegments(List<BucketSegment> bucketSegments) {
    }

    public List<RowFormatSegment> getRowFormatSegments() {
        return Collections.emptyList();
    }

    public void setRowFormatSegments(List<RowFormatSegment> rowFormatSegments) {
    }

    public List<StoredSegment> getStoredSegments() {
        return Collections.emptyList();
    }

    public void setStoredSegments(List<StoredSegment> storedSegments) {
    }

    public List<LocationSegment> getLocationSegments() {
        return Collections.emptyList();
    }

    public void setLocationSegments(List<LocationSegment> locationSegments) {
    }

    public List<CommentSegment> getCommentSegments() {
        return Collections.emptyList();
    }

    public void setCommentSegments(List<CommentSegment> commentSegments) {
    }

    public List<CollationSegment> getCollationSegments() {
        return Collections.emptyList();
    }

    public void setCollationSegments(List<CollationSegment> collationSegments) {
    }

    public Map<String, String> getTblProperties() {
        return Collections.emptyMap();
    }
}
