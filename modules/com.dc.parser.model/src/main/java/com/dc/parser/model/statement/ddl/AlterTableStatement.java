
package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.column.alter.*;
import com.dc.parser.model.segment.ddl.constraint.DropForeignKeySegment;
import com.dc.parser.model.segment.ddl.constraint.DropPrimaryKeySegment;
import com.dc.parser.model.segment.ddl.constraint.alter.AddConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.DropConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.ModifyConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.ValidateConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.index.DropIndexDefinitionSegment;
import com.dc.parser.model.segment.ddl.index.RenameIndexDefinitionSegment;
import com.dc.parser.model.segment.ddl.table.ConvertTableDefinitionSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;
import java.util.Optional;

/**
 * Alter table statement.
 */
@Getter
@Setter
public abstract class AlterTableStatement extends AbstractSQLStatement implements DDLStatement {
    
    private SimpleTableSegment table;
    
    private SimpleTableSegment renameTable;
    
    private ConvertTableDefinitionSegment convertTableDefinition;
    
    private final Collection<AddColumnDefinitionSegment> addColumnDefinitions = new LinkedList<>();
    
    private final Collection<ModifyColumnDefinitionSegment> modifyColumnDefinitions = new LinkedList<>();
    
    private final Collection<DropColumnDefinitionSegment> dropColumnDefinitions = new LinkedList<>();
    
    private final Collection<AddConstraintDefinitionSegment> addConstraintDefinitions = new LinkedList<>();
    
    private final Collection<ValidateConstraintDefinitionSegment> validateConstraintDefinitions = new LinkedList<>();
    
    private final Collection<ModifyConstraintDefinitionSegment> modifyConstraintDefinitions = new LinkedList<>();
    
    private final Collection<DropConstraintDefinitionSegment> dropConstraintDefinitions = new LinkedList<>();
    
    private final Collection<DropIndexDefinitionSegment> dropIndexDefinitions = new LinkedList<>();

    private final Collection<DropForeignKeySegment> dropForeignKeyDefinitions = new LinkedList<>();

    private final Collection<DropPrimaryKeySegment> dropPrimaryKeyDefinitions = new LinkedList<>();
    
    private final Collection<RenameColumnSegment> renameColumnDefinitions = new LinkedList<>();
    
    private final Collection<RenameIndexDefinitionSegment> renameIndexDefinitions = new LinkedList<>();

    /**
     * Set modify collection retrieval.
     *
     * @param modifyCollectionRetrieval modify collection retrieval
     */
    public void setModifyCollectionRetrieval(final ModifyCollectionRetrievalSegment modifyCollectionRetrieval) {
    }
    
    /**
     * Get rename table.
     *
     * @return rename table
     */
    public Optional<SimpleTableSegment> getRenameTable() {
        return Optional.ofNullable(renameTable);
    }
    
    /**
     * Get convert table definition.
     *
     * @return convert table definition
     */
    public Optional<ConvertTableDefinitionSegment> getConvertTableDefinition() {
        return Optional.ofNullable(convertTableDefinition);
    }

    /**
     * Get modify collection retrieval.
     *
     * @return modify collection retrieval
     */
    public Optional<ModifyCollectionRetrievalSegment> getModifyCollectionRetrieval() {
        return Optional.empty();
    }
}
