
package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.ddl.table.AlgorithmTypeSegment;
import com.dc.parser.model.segment.ddl.table.LockTableSegment;
import com.dc.parser.model.segment.ddl.tablespace.TablespaceSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;
import java.util.Optional;

/**
 * Create index statement.
 */
@Getter
@Setter
public abstract class CreateIndexStatement extends AbstractSQLStatement implements DDLStatement {
    
    private IndexSegment index;
    
    private SimpleTableSegment table;

    private final Collection<ColumnSegment> columns = new LinkedList<>();

    /**
     * Get generated index start index.
     *
     * @return generated index start index
     */
    public Optional<Integer> getGeneratedIndexStartIndex() {
        return Optional.empty();
    }

    /**
     * Set generated index start index.
     *
     * @param generatedIndexStartIndex generated index start index
     */
    public void setGeneratedIndexStartIndex(final Integer generatedIndexStartIndex) {
    }

    /**
     * Judge whether contains if not exists or not.
     *
     * @return whether contains if not exists or not
     */
    public boolean isIfNotExists() {
        return false;
    }

    /**
     * Set if not exists or not.
     *
     * @param ifNotExists if not exists or not
     */
    public void setIfNotExists(final boolean ifNotExists) {
    }

    /**
     * Get algorithm type.
     *
     * @return algorithm type
     */
    public Optional<AlgorithmTypeSegment> getAlgorithmType() {
        return Optional.empty();
    }

    /**
     * Set algorithm type.
     *
     * @param algorithmType algorithm type
     */
    public void setAlgorithmType(final AlgorithmTypeSegment algorithmType) {
    }

    /**
     * Get lock table.
     *
     * @return lock table
     */
    public Optional<LockTableSegment> getLockTable() {
        return Optional.empty();
    }

    /**
     * Set lock table.
     *
     * @param lockTable lock table
     */
    public void setLockTable(final LockTableSegment lockTable) {
    }

    public TablespaceSegment getTablespace() {
        return null;
    }
}
