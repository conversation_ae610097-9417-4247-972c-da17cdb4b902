
package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.routine.FunctionNameSegment;
import com.dc.parser.model.segment.ddl.routine.RoutineBodySegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Create function statement.
 */
@Getter
@Setter
public abstract class CreateFunctionStatement extends AbstractSQLStatement implements DDLStatement {
    
    private FunctionNameSegment functionName;
    
    /**
     * Get function name segment.
     *
     * @return function name segment
     */
    public Optional<FunctionNameSegment> getFunctionName() {
        return Optional.ofNullable(functionName);
    }

    /**
     * Get routine body.
     *
     * @return routine body
     */
    public Optional<RoutineBodySegment> getRoutineBody() {
        return Optional.empty();
    }

}
