package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.charset.CharsetNameSegment;
import com.dc.parser.model.segment.ddl.collation.CollationNameSegment;
import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Alter Database statement.
 */
@Getter
@Setter
public abstract class AlterDatabaseStatement extends AbstractSQLStatement implements DDLStatement {

    private DatabaseSegment databaseName;

    /**
     * Get database name.
     *
     * @return database name
     */
    public Optional<DatabaseSegment> getDatabaseName() {
        return Optional.ofNullable(databaseName);
    }

    /**
     * Get collation name.
     *
     * @return collation name
     */
    public Optional<CollationNameSegment> getCollationName() {
        return Optional.empty();
    }

    /**
     * Get charset name.
     *
     * @return charset name
     */
    public Optional<CharsetNameSegment> getCharsetName() {
        return Optional.empty();
    }
}
