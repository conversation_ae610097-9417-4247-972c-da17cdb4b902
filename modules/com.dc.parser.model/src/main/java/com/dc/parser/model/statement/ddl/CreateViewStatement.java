package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Create view statement.
 */
@Getter
@Setter
public abstract class CreateViewStatement extends AbstractSQLStatement implements DDLStatement {

    private boolean replaceView;
    
    private SimpleTableSegment view;
    
    private String viewDefinition;
    
    private SelectStatement select;
}
