package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.trigger.TriggerNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Alter trigger statement.
 */
@Getter
@Setter
public abstract class AlterTriggerStatement extends AbstractSQLStatement implements DDLStatement {

    private TriggerNameSegment triggerNameSegment;

}
